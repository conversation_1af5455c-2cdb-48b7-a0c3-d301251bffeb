
from typing import Annotated
from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Depends, status
from fastapi.security import OAuth2PasswordRequestForm

from schemas.users import Token, User
from services.auth import AuthService, get_current_user

router = APIRouter()

@router.post(
    "/token",
    response_model=Token,
    summary="Login for access token"
)
async def login_for_access_token(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()]
):
    """Login for access token"""
    access_token = AuthService.authenticate_user(
        form_data.username, form_data.password)

    if not access_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return {"access_token": access_token, "token_type": "bearer"}


@router.get("/users/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_user)):
    return current_user
