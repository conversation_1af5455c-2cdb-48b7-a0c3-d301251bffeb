terraform {
  backend "s3" {
    bucket = "ciphersense-ai-prod-infra-tfstate"
    key    = "cropsense/marketplace-api/terraform.tfstate"
    region = "eu-central-1"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = "eu-central-1"
}


##############################
# ECR Repository for the Image
##############################
resource "aws_ecr_repository" "cropsense-backend-marketplace-api" {
  name = "cropsense-backend-marketplace-api"

  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_lifecycle_policy" "cropsense-backend-marketplace-api" {
  repository = aws_ecr_repository.cropsense-backend-marketplace-api.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep only the last 3 images"
        selection = {
          tagStatus   = "any"
          countType   = "imageCountMoreThan"
          countNumber = 3
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}



##################################################
# IAM Role for Lambda Function (with Basic Policy)
##################################################
# IAM Role for Lambda Function
resource "aws_iam_role" "cropsense_marketplace_lambda_exec_role" {
  name = "cropsense_marketplace_lambda_exec_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action = "sts:AssumeRole",
      Effect = "Allow",
      Principal = {
        Service = "lambda.amazonaws.com"
      }
    }]
  })
}

# Basic Lambda execution policy
resource "aws_iam_role_policy_attachment" "cropsense_marketplace_lambda_exec_role_policy" {
  role       = aws_iam_role.cropsense_marketplace_lambda_exec_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "secrets_manager_read_only_attachment" {
  role       = aws_iam_role.cropsense_marketplace_lambda_exec_role.name
  policy_arn = "arn:aws:iam::aws:policy/SecretsManagerReadWrite" 
}

##################################################
# Lambda Function for the marketplace API (Python 3.11) - Staging
##################################################

# NOTE: Ensure that the image is built and pushed to the ECR repository before running this script.
resource "aws_lambda_function" "cropsense_backend_marketplace_api_lambda_staging" {
  function_name = "cropsense-backend-marketplace-api-lambda-staging"
  role          = aws_iam_role.cropsense_marketplace_lambda_exec_role.arn
  package_type  = "Image"

  # Replace ":latest" with your desired tag if needed.
  image_uri = "${aws_ecr_repository.cropsense-backend-marketplace-api.repository_url}:latest"

  timeout     = 60
  memory_size = 512

  environment {
    variables = {
      ENV                           = var.ENV,
      DB_URI                        = var.DB_URI,
      DB_SCHEMA                     = var.DB_SCHEMA,
      JWT_SECRET_KEY                = var.JWT_SECRET_KEY,
      JWT_REFRESH_SECRET_KEY        = var.JWT_REFRESH_SECRET_KEY,
      AWS_S3_BUCKET                 = var.AWS_S3_BUCKET,
      SENTRY_DSN                    = var.SENTRY_DSN,
      BASE_URL                      = var.BASE_URL,
    }
  }
}

resource "aws_lambda_function_url" "cropsense_backend_marketplace_api_lambda_staging" {
  function_name      = aws_lambda_function.cropsense_backend_marketplace_api_lambda_staging.function_name
  authorization_type = "NONE"

  cors {
    allow_credentials = true
    allow_origins     = ["*"] # You may want to restrict this to specific origins in production
    allow_methods     = ["*"]
    allow_headers     = ["*"]
    expose_headers    = ["*"]
    max_age           = 86400
  }
}


