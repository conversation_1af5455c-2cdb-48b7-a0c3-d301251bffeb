from fastapi import APIRouter, Depends, Request
from uuid import UUID
from schemas.organization_members import OrganizationMember, OrganizationMemberCreate, OrganizationMemberUpdate, OrganizationMemberList
from schemas.users import User
from services.organization_members import OrganizationMemberService
from services.auth import get_current_user

router = APIRouter()
current_user = Depends(get_current_user)

@router.post(
    "/create", 
    response_model=OrganizationMember, 
    summary="Create a new organization member"
)
def create_organization_member(
    data: OrganizationMemberCreate, 
     current_user: User = current_user,
):
    """
    Create a new organization member.
    """
    response = OrganizationMemberService().create_organization_member(member=data)
    return response

@router.get(
    "/get/all",
    response_model=OrganizationMemberList,
    summary = "Read all organizations and members"
)
def read_all_organization_members(
    skip: int = 0, 
    limit: int = 100, 
    current_user: User = current_user
):
    """
    Read list of all organization members
    """
    response = OrganizationMemberService().get_all_organization_members(skip=skip, limit=limit)
    return response

@router.get(
    "/{org_id}", 
    response_model=OrganizationMemberList, 
    summary="Read a list of organization members"
)
def read_organization_members(
    org_id: str,
    skip: int = 0, 
    limit: int = 100, 
     current_user: User = current_user,
):
    """
    Read a list of organization members.
    """
    response = OrganizationMemberService().get_organization_members(organization_id=org_id, skip=skip, limit=limit)
    return response

@router.get(
    "/{member_id}", 
    response_model=OrganizationMember, 
    summary="Read a single organization member"
)
def read_organization_member(
    member_id: str, 
     current_user: User = current_user,
):
    """
    Read a single organization member.
    """
    db_org_member = OrganizationMemberService().get_organization_member_by_id(member_id=member_id)
    return db_org_member

@router.put(
    "/{member_id}", 
    response_model=OrganizationMember, 
    summary="Update an organization member"
)
def update_organization_member(
    member_id: str, 
    member: OrganizationMemberUpdate, 
     current_user: User = current_user,
):
    """
    Update an organization member.
    """
    db_org_member = OrganizationMemberService().update_organization_member(member_id=member_id, member=member)
    return db_org_member

@router.delete(
    "/{member_id}", 
    response_model=None, 
    summary="Delete an organization member"
)
def delete_organization_member(
    member_id: str, 
     current_user: User = current_user,
):
    """
    Delete an organization member.
    """
    db_org_member = OrganizationMemberService().delete_organization_member(member_id=member_id)
    return db_org_member
