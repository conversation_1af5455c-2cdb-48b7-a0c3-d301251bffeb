
import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Database connection parameters
DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT")
DB_NAME = os.getenv("DB_NAME")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_SCHEMA = os.getenv("DB_SCHEMA") or "marketplace_dev"


def create_tables():
    """ create tables in the PostgreSQL database"""
    commands = (
        """
        CREATE TABLE IF NOT EXISTS marketplace_dev.users (
            user_id VARCHAR(255) PRIMARY KEY,
            email VARCHAR(255) NOT NULL UNIQUE,
            first_name VA<PERSON>HAR(255) NOT NULL,
            last_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
            hashed_password VARCHAR(255) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            is_superuser BOOLEAN DEFAULT FALSE,
            is_verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc'),
            updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc')
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS marketplace_dev.marketplace_api_requests_logs (
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            ip_address VARCHAR(255) NOT NULL,
            path VARCHAR(255) NOT NULL,
            request_body JSONB,
            response_body JSONB,
            created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc')
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS marketplace_dev.organizations (
            organization_id VARCHAR(255) PRIMARY KEY,
            organization_name VARCHAR(100) NOT NULL UNIQUE,
            organization_description TEXT,
            contact_email VARCHAR(255) NOT NULL,
            contact_phone VARCHAR(20),
            website VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc'),
            updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc')
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS marketplace_dev.organization_items (
            item_id VARCHAR(255) PRIMARY KEY,
            organization_id VARCHAR(255) NOT NULL,
            item_name VARCHAR(200) NOT NULL,
            item_description TEXT NOT NULL,
            item_price NUMERIC(10, 2) NOT NULL,
            currency VARCHAR(3) NOT NULL DEFAULT 'NGN',
            stock_quantity INTEGER DEFAULT 0,
            min_order_quantity INTEGER DEFAULT 1,
            item_type VARCHAR(255) NOT NULL,
            pricing_model VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc'),
            updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc')
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS marketplace_dev.orders (
            order_id VARCHAR(255) PRIMARY KEY,
            order_status VARCHAR(255) NOT NULL,
            buyer_id VARCHAR(255) NOT NULL,
            currency VARCHAR(3) NOT NULL DEFAULT 'NGN',
            sub_total NUMERIC(10, 2) NOT NULL,
            vat_total NUMERIC(10, 2) DEFAULT 0.00,
            total_amount NUMERIC(10, 2) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc'),
            updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc')
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS marketplace_dev.order_items (
            order_item_id SERIAL PRIMARY KEY,
            order_id VARCHAR(255) NOT NULL,
            seller_id VARCHAR(255) NOT NULL,
            item_id VARCHAR(255) NOT NULL,
            item_type VARCHAR(255) NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price NUMERIC(10, 2) NOT NULL,
            sub_total NUMERIC(10, 2) NOT NULL,
            vat NUMERIC(10, 2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc'),
            updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc')
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS marketplace_dev.organization_members (
            member_id VARCHAR(255) PRIMARY KEY,
            organization_id VARCHAR(255) NOT NULL,
            user_id VARCHAR(255) NOT NULL,
            role VARCHAR(255) NOT NULL,
            accept_invite BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc'),
            updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc')
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS marketplace_dev.order_payments (
            payment_id VARCHAR(255) PRIMARY KEY,
            order_id VARCHAR(255) NOT NULL,
            payment_status VARCHAR(255) NOT NULL,
            payment_method VARCHAR(255) NOT NULL,
            amount_paid NUMERIC(10, 2) NOT NULL,
            transaction_id VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc'),
            updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() AT TIME ZONE 'utc')
        );
        """
    )
    conn = None
    try:
        # connect to the PostgreSQL server
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        cur = conn.cursor()
        # create table one by one
        for command in commands:
            cur.execute(command)
        # close communication with the PostgreSQL database server
        cur.close()
        # commit the changes
        conn.commit()
    except (Exception, psycopg2.DatabaseError) as error:
        print(error)
    finally:
        if conn is not None:
            conn.close()


if __name__ == '__main__':
    create_tables()
