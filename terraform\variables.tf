####################################################
# Secrets Variables
####################################################

variable "ENV" {
  description = "The environment"
  type        = string
  default     = "staging"
}

variable "DB_SCHEMA" {
  description = "The name of the database schema"
  type        = string
}

variable "DB_URI" {
  description = "The URI of the database"
  type        = string
}

variable "AWS_SECRET_ACCESS_KEY" {
  description = "The AWS secret access key"
  type        = string
}

variable "AWS_ACCESS_KEY_ID" {
  description = "The AWS access key ID"
  type        = string
}

variable "AWS_S3_BUCKET" {
  description = "The AWS S3 bucket"
  type        = string
}

variable "AWS_REGION" {
  description = "The AWS region"
  type        = string
  default     = "eu-central-1"
}

variable "JWT_SECRET_KEY" {
  description = "The secret key for JWT"
  type        = string
}

variable "JWT_REFRESH_SECRET_KEY" {
  description = "The secret key for JWT refresh"
  type        = string
}

variable "SENTRY_DSN" {
  description = "The Sentry DSN"
  type        = string
}

variable "BASE_URL" {
  description = "The URL of the base API"
  type        = string
}
