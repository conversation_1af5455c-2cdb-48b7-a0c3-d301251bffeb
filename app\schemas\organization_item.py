from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from decimal import Decimal


class OrganizationItemCreate(BaseModel):
    organization_id: str
    item_name: str
    item_description: str
    item_type: str
    item_price: Decimal
    currency: Optional[str] = "NGN"
    stock_quantity: Optional[int] = 0
    min_order_quantity: Optional[int] = 1
    pricing_model: Optional[str] = None


class OrganizationItemUpdate(BaseModel):
    item_name: Optional[str] = None
    item_description: Optional[str] = None
    item_price: Optional[Decimal] = None
    item_type: Optional[str] = None
    currency: Optional[str] = None
    stock_quantity: Optional[int] = None
    min_order_quantity: Optional[int] = None
    pricing_model: Optional[str] = None
    is_active: Optional[bool] = None


class OrganizationItem(BaseModel):
    item_id: str
    organization_id: str
    item_name: str
    item_description: str
    item_type: str
    item_price: Decimal
    currency: str
    stock_quantity: int
    min_order_quantity: int
    pricing_model: Optional[str]
    is_active: bool
    created_at: datetime
    updated_at: datetime


    class Config:
        from_attributes = True


class OrganizationItemList(BaseModel):
    items: List[OrganizationItem]
    total: int


    class Config:
        from_attributes = True
