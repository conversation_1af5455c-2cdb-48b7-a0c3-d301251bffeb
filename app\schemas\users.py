
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel


class TokenData(BaseModel):
    email: Optional[str] = None

    class Config:
        from_attributes = True


class Token(BaseModel):
    access_token: str
    token_type: str

    class Config:
        from_attributes = True


class User(BaseModel):
    user_id: str
    email: str
    first_name: str
    last_name: str
    is_active: bool
    is_superuser: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
