from fastapi import APIRouter, Depends
from schemas.order import Order, OrderCreate, OrderList, OrderUpdate
from schemas.users import User
from services.order import OrderService
from services.auth import get_current_user

router = APIRouter()
current_user = Depends(get_current_user)

@router.post(
    "/create", 
    response_model=Order, 
    summary="Create a new order"
    )
def create_order(
    data: OrderCreate, 
    current_user: User = current_user,
):
    """
    Create a new order.
    """
    response = OrderService().create_order(order=data, user_id=current_user.user_id)
    return response

@router.get(
    "/get/all", 
    response_model=OrderList, 
    summary="Read a list of orders")
def read_orders(
    skip: int = 0, 
    limit: int = 100, 
    current_user: User = current_user,
):
    """
    Read a list of orders.
    """
    response = OrderService().get_orders(skip=skip, limit=limit)
    return response

@router.get(
    "/{order_id}", 
    response_model=Order, 
    summary="Read a single order"
    )
def read_order(
    order_id: str, 
    current_user: User = current_user,
):
    """
    Read a single order.
    """
    db_order = OrderService().get_order_by_id(order_id=order_id)
    return db_order

@router.get(
    "/user", 
    response_model=OrderList, 
    summary="Read a list of orders by user ID"
)
def read_orders_by_user_id(
    skip: int = 0, 
    limit: int = 100,
    current_user: User = current_user,
):
    """
    Read a list of orders by user ID.
    """
    print(current_user)
    response = OrderService().get_orders_by_user_id(user_id=current_user.user_id, skip=skip, limit=limit)
    return response

@router.put(
    "/{order_id}", 
    response_model=Order, 
    summary="Update an order"
)
def update_order(
    order_id: str, 
    order: OrderUpdate,
    current_user: User = current_user,
):
    """
    Update an order.
    """
    db_order = OrderService().update_order(order_id=order_id, order=order)
    return db_order

@router.delete(
    "/{order_id}", 
    response_model=Order, 
    summary="Delete an order"
)
def delete_order(
    order_id: str,
    current_user: User = current_user,
):
    """
    Delete an order.
    """
    db_order = OrderService().delete_order(order_id=order_id)
    return db_order
