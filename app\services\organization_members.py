from fastapi import HTTPException, status
from datetime import datetime
from sqlalchemy.orm import Session
from uuid import UUID
from models import OrganizationMember, Organization, Users
from schemas.organization_members import OrganizationMemberCreate, OrganizationMemberUpdate
from config import db

class OrganizationMemberService:
    def get_organization_member_by_id(self, member_id: str) -> OrganizationMember:
        """
        Get a single organization member by ID.

        Args:
            member_id (str): The ID of the organization member to get.

        Raises:
            HTTPException: If the organization member is not found (404).

        Returns:
            OrganizationMember: The organization member, if found.
        """
        db_org_member = db.query(OrganizationMember).filter(OrganizationMember.member_id == str(member_id)).first()
        if not db_org_member:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization member not found.")
        return db_org_member
    def get_all_organization_members(self, skip: int = 0, limit: int = 100) -> dict:
        """
        Get a flat list of all organization members.

        Returns:
            dict: A dictionary containing the list of all organization members and the total count.
        """
        members = db.query(OrganizationMember).offset(skip).limit(limit).all()
        total = db.query(OrganizationMember).count()
        return {"organization_members": members, "total": total}

    def get_organization_members(self, organization_id: str, skip: int = 0, limit: int = 100) -> dict:
        """
        Get a list of organization members.

        Args:
            organization_id (str): The ID of the organization.
            skip (int, optional): The number of organization members to skip. Defaults to 0.
            limit (int, optional): The maximum number of organization members to return. Defaults to 100.

        Returns:
            dict: A dictionary containing the list of organization members and the total count.
        """
        organization_members = db.query(OrganizationMember).filter(OrganizationMember.organization_id == organization_id).offset(skip).limit(limit).all()
        total = db.query(OrganizationMember).filter(OrganizationMember.organization_id == organization_id).count()
        return {"organization_members": organization_members, "total": total}

    def create_organization_member(self, member: OrganizationMemberCreate) -> OrganizationMember:
        """
        Create a new organization member.

        Args:
            member (OrganizationMemberCreate): The data to create the organization member with.

        Raises:
            HTTPException: If there is an error creating the organization member (500).

        Returns:
            OrganizationMember: The created organization member.
        """
        try:
            # Check if organization exists
            organization = db.query(Organization).filter(Organization.organization_id == member.organization_id).first()
            if not organization:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found.")

            # Check if user exists
            user = db.query(Users).filter(Users.user_id == member.user_id).first()
            if not user:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found.")

            # Check if user is already a member of the organization
            existing_member = db.query(OrganizationMember).filter(
                OrganizationMember.organization_id == member.organization_id,
                OrganizationMember.user_id == member.user_id
            ).first()
            if existing_member:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User is already a member of this organization.")

            db_org_member = OrganizationMember(**member.dict())
            db.add(db_org_member)
            db.commit()
            db.refresh(db_org_member)
            return db_org_member
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error creating organization member: {e}")

    def update_organization_member(self, member_id: str, member: OrganizationMemberUpdate) -> OrganizationMember:
        """
        Update an existing organization member.

        Args:
            member_id (str): The ID of the organization member to update.
            member (OrganizationMemberUpdate): The data to update the organization member with.

        Raises:
            HTTPException: If the organization member is not found (404) or if there is an error updating the organization member (500).

        Returns:
            OrganizationMember: The updated organization member.
        """
        try:
            db_org_member = self.get_organization_member_by_id(member_id)
            update_data = member.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_org_member, key, value)
            db_org_member.updated_at = datetime.utcnow() # Explicitly update the timestamp
            db.commit()
            db.refresh(db_org_member)
            return db_org_member
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error updating organization member: {e}")

    def delete_organization_member(self, member_id: str) -> OrganizationMember:
        """
        Soft delete an organization member by setting is_active to False.

        Args:
            member_id (str): The ID of the organization member to delete.

        Returns:
            OrganizationMember: The updated organization member.
        """
        try:
            db_org_member = self.get_organization_member_by_id(member_id)
            db_org_member.is_active = False
            db_org_member.updated_at = datetime.utcnow()  # Update timestamp
            db.commit()
            db.refresh(db_org_member)
            return db_org_member
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error deleting organization member: {e}")
