from typing import Annotated
import requests

from passlib.context import Crypt<PERSON>ontext
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer

from config import db, secrets, logger
from schemas.users import User
import os


BASE_URL = os.getenv("BASE_URL")

# OAuth2
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token")
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """Auth services"""

    @staticmethod
    def authenticate_user(email: str, password: str) -> str:
        """Authenticate a user"""
        
        payload = {
            "username": email,
            "password": password
        }
        headers = {
            "accept": "application/json",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        try:
            response = requests.post(
                f"{BASE_URL}/auth/token",
                data=payload,
                headers=headers
            )
            response.raise_for_status()
            return response.json().get("access_token")
        except requests.exceptions.RequestException as e:
            logger.error(f"Error authenticating user: {e}")
            return None



async def get_current_user(token: Annotated[str, Depends(oauth2_scheme)]) -> User:
    """Get the current user from the token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid authentication credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        response = requests.get(
            f"{BASE_URL}/users/me",
            headers={"Authorization": f"Bearer {token}"}
        )

        response.raise_for_status()
        return User(**response.json())
    except requests.exceptions.RequestException as e:
        logger.error(f"Error getting current user: {e}")
        raise credentials_exception from e