from fastapi import APIRouter, Depends
from schemas.order_payment import OrderPayment, OrderPaymentCreate, OrderPaymentUpdate
from schemas.users import User
from services.order_payment import OrderPaymentService
from services.auth import get_current_user

router = APIRouter()
current_user = Depends(get_current_user)

@router.post(
    "/create", 
    response_model=OrderPayment, 
    summary="Create a new order payment"
    )
def create_order_payment(
    data: OrderPaymentCreate, 
    current_user: User = current_user,
):
    """
    Create a new order payment.
    """
    response = OrderPaymentService().create_order_payment(order_payment=data)
    return response

@router.get(
    "/{order_id}", 
    response_model=list[OrderPayment], 
    summary="Read a list of order payments by order ID"
)
def read_order_payments_by_order_id(
    order_id: str,
    skip: int = 0, 
    limit: int = 100,
    current_user: User = current_user,
):
    """
    Read a list of order payments by order ID.
    """
    response = OrderPaymentService().get_order_payments_by_order_id(order_id=order_id, skip=skip, limit=limit)
    return response['order_payments']

@router.get(
    "/payment/{payment_id}", 
    response_model=OrderPayment, 
    summary="Read a single order payment"
    )
def read_order_payment(
    payment_id: str, 
    current_user: User = current_user,
):
    """
    Read a single order payment.
    """
    db_order_payment = OrderPaymentService().get_order_payment_by_id(payment_id=payment_id)
    return db_order_payment

@router.put(
    "/{payment_id}", 
    response_model=OrderPayment, 
    summary="Update an order payment"
)
def update_order_payment(
    payment_id: str, 
    order_payment: OrderPaymentUpdate,
    current_user: User = current_user,
):
    """
    Update an order payment.
    """
    db_order_payment = OrderPaymentService().update_order_payment(payment_id=payment_id, order_payment=order_payment)
    return db_order_payment